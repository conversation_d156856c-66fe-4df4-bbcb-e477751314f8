# Plasma - Web Development & Graphic Design Agency

A modern, responsive website for Plasma Agency featuring cutting-edge design, smooth animations, and dark mode functionality. Built with HTML5, Tailwind CSS, and vanilla JavaScript.

![Plasma Agency](https://img.shields.io/badge/Status-Live-brightgreen)
![HTML5](https://img.shields.io/badge/HTML5-E34F26?style=flat&logo=html5&logoColor=white)
![CSS3](https://img.shields.io/badge/CSS3-1572B6?style=flat&logo=css3&logoColor=white)
![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=flat&logo=javascript&logoColor=black)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=flat&logo=tailwind-css&logoColor=white)

## ✨ Features

### 🎨 Design & UI/UX
- **Modern Glassmorphism Design** - Beautiful glass-like elements with backdrop blur effects
- **Responsive Layout** - Optimized for all devices (mobile, tablet, desktop)
- **Dark Mode Support** - Toggle between light and dark themes with smooth transitions
- **Gradient Animations** - Dynamic color gradients with smooth animations
- **Micro-interactions** - Hover effects, scale transforms, and smooth transitions

### 🚀 Performance & Functionality
- **Fast Loading** - Optimized assets and efficient code structure
- **Smooth Scrolling** - Enhanced navigation experience
- **Cross-browser Compatible** - Works on all modern browsers
- **SEO Optimized** - Proper meta tags and semantic HTML structure
- **Accessibility Ready** - ARIA labels and keyboard navigation support

### 📱 Sections
- **Hero Section** - Eye-catching landing area with animated elements
- **Services** - Web Development and Graphic Design offerings
- **About** - Company information and statistics
- **Portfolio** - Showcase of recent projects
- **Contact** - Contact form and company information
- **Footer** - Additional links and social media

## 🛠️ Technologies Used

- **HTML5** - Semantic markup and structure
- **Tailwind CSS** - Utility-first CSS framework via CDN
- **JavaScript (ES6+)** - Modern vanilla JavaScript for interactions
- **Google Fonts** - Inter font family for typography
- **CSS Animations** - Custom keyframe animations and transitions

## 🚀 Quick Start

### Prerequisites
- A modern web browser (Chrome, Firefox, Safari, Edge)
- Basic understanding of HTML/CSS/JavaScript (for customization)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/plasma-agency.git
   cd plasma-agency
   ```

2. **Open the website**
   ```bash
   # Simply open index.html in your browser
   open index.html
   # or
   double-click index.html
   ```

3. **For development with live server**
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js (if you have live-server installed)
   npx live-server
   
   # Using PHP
   php -S localhost:8000
   ```

## 🎨 Customization

### Colors
The website uses a custom color palette defined in the Tailwind configuration:

```javascript
colors: {
    'plasma-blue': '#3B82F6',
    'plasma-light': '#EFF6FF',
    'plasma-gray': '#F8FAFC',
    'plasma-dark': '#0F172A',
    'plasma-accent': '#06B6D4',
}
```

### Dark Mode
Dark mode is implemented using Tailwind's dark mode classes and JavaScript:

- **Toggle Buttons**: Available in both desktop and mobile navigation
- **Local Storage**: User preference is saved and persisted
- **Smooth Transitions**: 500ms transition duration for theme switching

### Animations
Custom animations are defined in the Tailwind config:

- `float` - Floating elements animation (6s duration)
- `glow` - Glowing effect for interactive elements
- `slide-up` - Slide up animation for content reveal
- `fade-in` - Fade in animation with delays

## 📁 Project Structure

```
plasma-agency/
├── index.html          # Main HTML file
├── README.md          # Project documentation
└── assets/            # (Optional) Additional assets
    ├── images/        # Image files
    ├── icons/         # Icon files
    └── docs/          # Additional documentation
```

## 🔧 Configuration

### Tailwind CSS Configuration
The project uses Tailwind CSS via CDN with custom configuration:

```javascript
tailwind.config = {
    darkMode: 'class',
    theme: {
        extend: {
            // Custom colors, fonts, animations
        }
    }
}
```

### JavaScript Features
- Dark mode toggle functionality
- Smooth scrolling navigation
- Form validation (ready for implementation)
- Mobile menu toggle (expandable)

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🎯 Browser Support

- ✅ Chrome (latest)
- ✅ Firefox (latest)
- ✅ Safari (latest)
- ✅ Edge (latest)
- ⚠️ Internet Explorer (not supported)

## 🚀 Deployment

### GitHub Pages
1. Push your code to a GitHub repository
2. Go to Settings > Pages
3. Select source branch (usually `main`)
4. Your site will be available at `https://yourusername.github.io/plasma-agency`

### Netlify
1. Connect your GitHub repository to Netlify
2. Set build command: (none needed for static site)
3. Set publish directory: `/` (root)
4. Deploy automatically on git push

### Vercel
1. Import your GitHub repository to Vercel
2. No build configuration needed
3. Deploy with zero configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Plasma Agency**
- Website: [plasma-agency.com](https://plasma-agency.com)
- Email: <EMAIL>
- Twitter: [@PlasmaAgency](https://twitter.com/PlasmaAgency)

## 🙏 Acknowledgments

- [Tailwind CSS](https://tailwindcss.com/) for the amazing utility-first framework
- [Google Fonts](https://fonts.google.com/) for the Inter font family
- [Heroicons](https://heroicons.com/) for the beautiful SVG icons
- Design inspiration from modern web design trends

---

⚡ **Built with passion by Plasma Agency** ⚡
