<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Plasma - Professional Web Development and Graphic Design Agency. Creating stunning digital experiences and visual identities for businesses worldwide.">
    <meta name="keywords" content="web development, graphic design, digital agency, website design, branding, UI/UX">
    <meta name="author" content="Plasma Agency">
    <title>Plasma - Web Development & Graphic Design Agency</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'plasma-blue': '#3B82F6',
                        'plasma-light': '#EFF6FF',
                        'plasma-gray': '#F8FAFC',
                        'plasma-dark': '#0F172A',
                        'plasma-accent': '#06B6D4',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in': 'fadeIn 0.6s ease-out',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)' },
                            '100%': { boxShadow: '0 0 30px rgba(59, 130, 246, 0.8)' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(30px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <style>
        html {
            scroll-behavior: smooth;
            font-family: 'Inter', system-ui, sans-serif;
        }

        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .glass-dark {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .gradient-mesh {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .text-gradient {
            background: linear-gradient(135deg, #3B82F6, #06B6D4, #8B5CF6);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 4s ease infinite;
        }

        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .morphism {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors duration-500">
    <!-- Navigation -->
    <nav class="fixed top-4 left-1/2 transform -translate-x-1/2 w-[95%] max-w-6xl glass dark:glass-dark rounded-2xl z-50 transition-all duration-500 hover:shadow-2xl">
        <div class="px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="text-2xl font-black text-gradient tracking-tight">
                    Plasma
                </div>
                <div class="hidden md:flex items-center space-x-8 bg-white/10 dark:bg-white/5 rounded-full px-6 py-2 backdrop-blur-sm">
                    <a href="#home" class="text-gray-700 dark:text-gray-300 hover:text-plasma-blue transition-all duration-300 font-medium text-sm tracking-wide hover:scale-105">Home</a>
                    <a href="#services" class="text-gray-700 dark:text-gray-300 hover:text-plasma-blue transition-all duration-300 font-medium text-sm tracking-wide hover:scale-105">Services</a>
                    <a href="#about" class="text-gray-700 dark:text-gray-300 hover:text-plasma-blue transition-all duration-300 font-medium text-sm tracking-wide hover:scale-105">About</a>
                    <a href="#portfolio" class="text-gray-700 dark:text-gray-300 hover:text-plasma-blue transition-all duration-300 font-medium text-sm tracking-wide hover:scale-105">Portfolio</a>
                    <a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-plasma-blue transition-all duration-300 font-medium text-sm tracking-wide hover:scale-105">Contact</a>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <!-- Dark Mode Toggle -->
                    <button id="darkModeToggle" class="p-2.5 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 hover:scale-105">
                        <svg id="sunIcon" class="w-5 h-5 text-gray-700 dark:text-gray-300 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        <svg id="moonIcon" class="w-5 h-5 text-gray-700 dark:text-gray-300 transition-all duration-300 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                    <a href="#contact" class="bg-plasma-blue text-white px-6 py-2.5 rounded-full font-semibold text-sm hover:bg-blue-600 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                        Get Started
                    </a>
                </div>
                <div class="md:hidden flex items-center space-x-2">
                    <!-- Mobile Dark Mode Toggle -->
                    <button id="darkModeToggleMobile" class="p-2 rounded-lg bg-white/10 dark:bg-white/5 hover:bg-white/20 dark:hover:bg-white/10 transition-all duration-300">
                        <svg id="sunIconMobile" class="w-5 h-5 text-gray-700 dark:text-gray-300 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        <svg id="moonIconMobile" class="w-5 h-5 text-gray-700 dark:text-gray-300 transition-all duration-300 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                    <button class="text-gray-700 dark:text-gray-300 hover:text-plasma-blue p-2 rounded-lg hover:bg-white/20 dark:hover:bg-white/10 transition-all duration-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-plasma-blue/10 rounded-full blur-3xl animate-float"></div>
            <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-plasma-accent/10 rounded-full blur-3xl animate-float" style="animation-delay: -3s;"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-plasma-blue/5 to-plasma-accent/5 rounded-full blur-3xl animate-pulse"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10 pt-20">
            <!-- Main Heading with Advanced Typography -->
            <div class="mb-8 animate-slide-up">
                <h1 class="text-7xl md:text-9xl font-black mb-4 tracking-tighter">
                    <span class="text-gradient">Plasma</span>
                </h1>
                <div class="w-24 h-1 bg-gradient-to-r from-plasma-blue to-plasma-accent mx-auto rounded-full"></div>
            </div>

            <!-- Subtitle with Modern Typography -->
            <div class="animate-fade-in" style="animation-delay: 0.2s;">
                <p class="text-2xl md:text-3xl font-light text-gray-700 dark:text-gray-300 mb-6 max-w-4xl mx-auto leading-tight tracking-wide">
                    Igniting <span class="font-semibold text-plasma-blue">digital experiences</span> through innovative web development and stunning graphic design
                </p>
            </div>

            <!-- Description -->
            <div class="animate-fade-in" style="animation-delay: 0.4s;">
                <p class="text-lg text-gray-500 dark:text-gray-400 mb-12 max-w-2xl mx-auto leading-relaxed font-light">
                    We transform your vision into powerful digital solutions that captivate audiences and drive results
                </p>
            </div>

            <!-- Modern CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center animate-fade-in" style="animation-delay: 0.6s;">
                <a href="#contact" class="group relative bg-plasma-blue text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-blue-600 transition-all duration-500 hover:scale-105 hover:shadow-2xl overflow-hidden">
                    <span class="relative z-10">Start Your Project</span>
                    <div class="absolute inset-0 bg-gradient-to-r from-plasma-blue to-plasma-accent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </a>
                <a href="#portfolio" class="group morphism text-plasma-blue px-8 py-4 rounded-2xl font-semibold text-lg hover:scale-105 transition-all duration-500 hover:shadow-2xl border border-white/30">
                    <span class="flex items-center justify-center gap-2">
                        View Our Work
                        <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </span>
                </a>
            </div>

            <!-- Scroll Indicator -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                <div class="w-6 h-10 border-2 border-gray-300 dark:border-gray-600 rounded-full flex justify-center">
                    <div class="w-1 h-3 bg-gray-400 dark:bg-gray-500 rounded-full mt-2 animate-pulse"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-32 bg-white dark:bg-gray-800 relative overflow-hidden transition-colors duration-500">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5 dark:opacity-10">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, #3B82F6 1px, transparent 0); background-size: 40px 40px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-24">
                <div class="inline-block mb-4">
                    <span class="bg-plasma-blue/10 text-plasma-blue px-4 py-2 rounded-full text-sm font-semibold tracking-wide uppercase">Services</span>
                </div>
                <h2 class="text-5xl md:text-7xl font-black mb-8 text-gray-900 dark:text-gray-100 tracking-tight">
                    What We <span class="text-gradient">Create</span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed font-light">
                    We offer comprehensive digital solutions to elevate your brand and online presence
                </p>
            </div>

            <!-- Services Grid -->
            <div class="grid lg:grid-cols-2 gap-12">
                <!-- Web Development -->
                <div class="group hover-lift morphism p-12 rounded-3xl relative overflow-hidden">
                    <!-- Gradient Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-br from-plasma-blue/5 to-plasma-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <!-- Icon with Glow Effect -->
                        <div class="w-20 h-20 bg-gradient-to-br from-plasma-blue to-plasma-accent rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-lg group-hover:shadow-plasma-blue/25">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                            </svg>
                        </div>

                        <h3 class="text-3xl font-bold mb-6 text-gray-900 group-hover:text-plasma-blue transition-colors duration-300">
                            Web Development
                        </h3>

                        <p class="text-gray-600 mb-10 leading-relaxed text-lg font-light">
                            Custom websites and web applications built with modern technologies. From responsive designs to complex e-commerce platforms, we create digital experiences that perform.
                        </p>

                        <!-- Feature List with Modern Design -->
                        <div class="space-y-4">
                            <div class="flex items-center group/item">
                                <div class="w-2 h-2 bg-gradient-to-r from-plasma-blue to-plasma-accent rounded-full mr-4 group-hover/item:scale-150 transition-transform duration-300"></div>
                                <span class="text-gray-700 font-medium group-hover/item:text-plasma-blue transition-colors duration-300">Responsive Web Design</span>
                            </div>
                            <div class="flex items-center group/item">
                                <div class="w-2 h-2 bg-gradient-to-r from-plasma-blue to-plasma-accent rounded-full mr-4 group-hover/item:scale-150 transition-transform duration-300"></div>
                                <span class="text-gray-700 font-medium group-hover/item:text-plasma-blue transition-colors duration-300">E-commerce Solutions</span>
                            </div>
                            <div class="flex items-center group/item">
                                <div class="w-2 h-2 bg-gradient-to-r from-plasma-blue to-plasma-accent rounded-full mr-4 group-hover/item:scale-150 transition-transform duration-300"></div>
                                <span class="text-gray-700 font-medium group-hover/item:text-plasma-blue transition-colors duration-300">Custom Web Applications</span>
                            </div>
                            <div class="flex items-center group/item">
                                <div class="w-2 h-2 bg-gradient-to-r from-plasma-blue to-plasma-accent rounded-full mr-4 group-hover/item:scale-150 transition-transform duration-300"></div>
                                <span class="text-gray-700 font-medium group-hover/item:text-plasma-blue transition-colors duration-300">Performance Optimization</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Graphic Design -->
                <div class="group hover-lift morphism p-12 rounded-3xl relative overflow-hidden">
                    <!-- Gradient Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-br from-plasma-accent/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <!-- Icon with Glow Effect -->
                        <div class="w-20 h-20 bg-gradient-to-br from-plasma-accent to-purple-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-lg group-hover:shadow-plasma-accent/25">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                            </svg>
                        </div>

                        <h3 class="text-3xl font-bold mb-6 text-gray-900 group-hover:text-plasma-accent transition-colors duration-300">
                            Graphic Design
                        </h3>

                        <p class="text-gray-600 mb-10 leading-relaxed text-lg font-light">
                            Visual identity and branding solutions that make your business stand out. From logos to complete brand systems, we create designs that communicate your unique story.
                        </p>

                        <!-- Feature List with Modern Design -->
                        <div class="space-y-4">
                            <div class="flex items-center group/item">
                                <div class="w-2 h-2 bg-gradient-to-r from-plasma-accent to-purple-500 rounded-full mr-4 group-hover/item:scale-150 transition-transform duration-300"></div>
                                <span class="text-gray-700 font-medium group-hover/item:text-plasma-accent transition-colors duration-300">Brand Identity Design</span>
                            </div>
                            <div class="flex items-center group/item">
                                <div class="w-2 h-2 bg-gradient-to-r from-plasma-accent to-purple-500 rounded-full mr-4 group-hover/item:scale-150 transition-transform duration-300"></div>
                                <span class="text-gray-700 font-medium group-hover/item:text-plasma-accent transition-colors duration-300">Logo & Visual Assets</span>
                            </div>
                            <div class="flex items-center group/item">
                                <div class="w-2 h-2 bg-gradient-to-r from-plasma-accent to-purple-500 rounded-full mr-4 group-hover/item:scale-150 transition-transform duration-300"></div>
                                <span class="text-gray-700 font-medium group-hover/item:text-plasma-accent transition-colors duration-300">Print & Digital Materials</span>
                            </div>
                            <div class="flex items-center group/item">
                                <div class="w-2 h-2 bg-gradient-to-r from-plasma-accent to-purple-500 rounded-full mr-4 group-hover/item:scale-150 transition-transform duration-300"></div>
                                <span class="text-gray-700 font-medium group-hover/item:text-plasma-accent transition-colors duration-300">UI/UX Design</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-32 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden transition-colors duration-500">
        <!-- Background Elements -->
        <div class="absolute top-20 right-20 w-72 h-72 bg-plasma-blue/5 rounded-full blur-3xl"></div>
        <div class="absolute bottom-20 left-20 w-96 h-96 bg-plasma-accent/5 rounded-full blur-3xl"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid lg:grid-cols-2 gap-20 items-center">
                <!-- Content -->
                <div class="order-2 lg:order-1">
                    <div class="mb-6">
                        <span class="bg-plasma-blue/10 text-plasma-blue px-4 py-2 rounded-full text-sm font-semibold tracking-wide uppercase">About Us</span>
                    </div>

                    <h2 class="text-5xl md:text-6xl font-black mb-10 text-gray-900 tracking-tight leading-tight">
                        About <span class="text-gradient">Plasma</span>
                    </h2>

                    <div class="space-y-6 mb-12">
                        <p class="text-xl text-gray-600 leading-relaxed font-light">
                            Founded on the principle that great design and development should be accessible to businesses of all sizes, Plasma has been creating digital experiences that matter since our inception.
                        </p>
                        <p class="text-lg text-gray-600 leading-relaxed font-light">
                            Our team of passionate designers and developers combines creativity with technical expertise to deliver solutions that not only look stunning but also perform exceptionally. We believe in the power of collaboration and work closely with our clients to bring their visions to life.
                        </p>
                    </div>

                    <!-- Stats with Modern Design -->
                    <div class="grid grid-cols-3 gap-8">
                        <div class="text-center group">
                            <div class="text-4xl md:text-5xl font-black text-gradient mb-3 group-hover:scale-110 transition-transform duration-300">50+</div>
                            <div class="text-gray-600 text-sm font-medium tracking-wide uppercase">Projects Completed</div>
                        </div>
                        <div class="text-center group">
                            <div class="text-4xl md:text-5xl font-black text-gradient mb-3 group-hover:scale-110 transition-transform duration-300">5+</div>
                            <div class="text-gray-600 text-sm font-medium tracking-wide uppercase">Years Experience</div>
                        </div>
                        <div class="text-center group">
                            <div class="text-4xl md:text-5xl font-black text-gradient mb-3 group-hover:scale-110 transition-transform duration-300">100%</div>
                            <div class="text-gray-600 text-sm font-medium tracking-wide uppercase">Client Satisfaction</div>
                        </div>
                    </div>
                </div>

                <!-- Visual Element -->
                <div class="order-1 lg:order-2 relative">
                    <div class="relative w-full h-[500px] rounded-3xl overflow-hidden">
                        <!-- Glassmorphism Container -->
                        <div class="absolute inset-0 morphism flex items-center justify-center">
                            <!-- Animated Elements -->
                            <div class="relative">
                                <!-- Central Icon -->
                                <div class="w-32 h-32 bg-gradient-to-br from-plasma-blue to-plasma-accent rounded-3xl flex items-center justify-center mx-auto mb-8 animate-float shadow-2xl">
                                    <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>

                                <!-- Floating Elements -->
                                <div class="absolute -top-8 -left-8 w-16 h-16 bg-plasma-blue/20 rounded-2xl animate-float" style="animation-delay: -1s;"></div>
                                <div class="absolute -bottom-8 -right-8 w-20 h-20 bg-plasma-accent/20 rounded-2xl animate-float" style="animation-delay: -2s;"></div>
                                <div class="absolute top-1/2 -right-12 w-12 h-12 bg-purple-500/20 rounded-xl animate-float" style="animation-delay: -3s;"></div>

                                <!-- Text -->
                                <p class="text-gray-700 font-semibold text-lg text-center">Energizing Digital Experiences</p>
                            </div>
                        </div>

                        <!-- Background Pattern -->
                        <div class="absolute inset-0 opacity-10">
                            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, #3B82F6 1px, transparent 0); background-size: 30px 30px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-32 bg-white dark:bg-gray-800 relative overflow-hidden transition-colors duration-500">
        <!-- Background Elements -->
        <div class="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-plasma-blue/5 to-plasma-accent/5 rounded-full blur-3xl"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-24">
                <div class="inline-block mb-4">
                    <span class="bg-plasma-blue/10 text-plasma-blue px-4 py-2 rounded-full text-sm font-semibold tracking-wide uppercase">Portfolio</span>
                </div>
                <h2 class="text-5xl md:text-7xl font-black mb-8 text-gray-900 tracking-tight">
                    Our <span class="text-gradient">Work</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-light">
                    Explore some of our recent projects that showcase our expertise in web development and graphic design
                </p>
            </div>

            <!-- Portfolio Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Portfolio Item 1 -->
                <div class="group cursor-pointer hover-lift">
                    <div class="relative overflow-hidden rounded-3xl morphism h-80 mb-8 group-hover:shadow-2xl transition-all duration-500">
                        <!-- Gradient Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-br from-plasma-blue/10 to-plasma-accent/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <!-- Content -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center transform group-hover:scale-105 transition-transform duration-500">
                                <div class="w-20 h-20 bg-gradient-to-br from-plasma-blue to-plasma-accent rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-plasma-blue/25 group-hover:rotate-6 transition-all duration-500">
                                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                                    </svg>
                                </div>
                                <p class="text-gray-700 font-semibold text-lg">E-commerce Platform</p>
                            </div>
                        </div>

                        <!-- Floating Elements -->
                        <div class="absolute top-4 right-4 w-3 h-3 bg-plasma-blue/30 rounded-full animate-pulse"></div>
                        <div class="absolute bottom-4 left-4 w-2 h-2 bg-plasma-accent/30 rounded-full animate-pulse" style="animation-delay: -1s;"></div>
                    </div>

                    <div class="px-2">
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-plasma-blue transition-colors duration-300">TechStore Online</h3>
                        <p class="text-gray-600 leading-relaxed font-light">Modern e-commerce solution with advanced filtering and seamless checkout experience</p>
                    </div>
                </div>

                <!-- Portfolio Item 2 -->
                <div class="group cursor-pointer hover-lift">
                    <div class="relative overflow-hidden rounded-3xl morphism h-80 mb-8 group-hover:shadow-2xl transition-all duration-500">
                        <!-- Gradient Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-br from-plasma-accent/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <!-- Content -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center transform group-hover:scale-105 transition-transform duration-500">
                                <div class="w-20 h-20 bg-gradient-to-br from-plasma-accent to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-plasma-accent/25 group-hover:rotate-6 transition-all duration-500">
                                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                                    </svg>
                                </div>
                                <p class="text-gray-700 font-semibold text-lg">Brand Identity</p>
                            </div>
                        </div>

                        <!-- Floating Elements -->
                        <div class="absolute top-4 right-4 w-3 h-3 bg-plasma-accent/30 rounded-full animate-pulse"></div>
                        <div class="absolute bottom-4 left-4 w-2 h-2 bg-purple-500/30 rounded-full animate-pulse" style="animation-delay: -1s;"></div>
                    </div>

                    <div class="px-2">
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-plasma-accent transition-colors duration-300">Creative Studio Rebrand</h3>
                        <p class="text-gray-600 leading-relaxed font-light">Complete visual identity redesign including logo, color palette, and brand guidelines</p>
                    </div>
                </div>

                <!-- Portfolio Item 3 -->
                <div class="group cursor-pointer hover-lift">
                    <div class="relative overflow-hidden rounded-3xl morphism h-80 mb-8 group-hover:shadow-2xl transition-all duration-500">
                        <!-- Gradient Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <!-- Content -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center transform group-hover:scale-105 transition-transform duration-500">
                                <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-purple-500/25 group-hover:rotate-6 transition-all duration-500">
                                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <p class="text-gray-700 font-semibold text-lg">Mobile App UI</p>
                            </div>
                        </div>

                        <!-- Floating Elements -->
                        <div class="absolute top-4 right-4 w-3 h-3 bg-purple-500/30 rounded-full animate-pulse"></div>
                        <div class="absolute bottom-4 left-4 w-2 h-2 bg-pink-500/30 rounded-full animate-pulse" style="animation-delay: -1s;"></div>
                    </div>

                    <div class="px-2">
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-purple-500 transition-colors duration-300">FinTech Mobile App</h3>
                        <p class="text-gray-600 leading-relaxed font-light">Intuitive mobile banking interface with focus on user experience and security</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-32 bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute top-20 right-20 w-80 h-80 bg-plasma-blue/5 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-20 left-20 w-96 h-96 bg-plasma-accent/5 rounded-full blur-3xl animate-pulse" style="animation-delay: -2s;"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-24">
                <div class="inline-block mb-4">
                    <span class="bg-plasma-blue/10 text-plasma-blue px-4 py-2 rounded-full text-sm font-semibold tracking-wide uppercase">Contact</span>
                </div>
                <h2 class="text-5xl md:text-7xl font-black mb-8 text-gray-900 tracking-tight">
                    Let's Work <span class="text-gradient">Together</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-light">
                    Ready to bring your vision to life? Get in touch and let's create something amazing together
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-20">
                <!-- Contact Form -->
                <div class="morphism p-12 rounded-3xl hover-lift">
                    <h3 class="text-3xl font-bold mb-10 text-gray-900">Send us a message</h3>
                    <form class="space-y-8">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="group">
                                <label class="block text-gray-700 font-semibold mb-3 group-focus-within:text-plasma-blue transition-colors duration-300">First Name</label>
                                <input type="text" class="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-plasma-blue/20 focus:border-plasma-blue transition-all duration-300 bg-white/80 backdrop-blur-sm hover:bg-white" placeholder="John">
                            </div>
                            <div class="group">
                                <label class="block text-gray-700 font-semibold mb-3 group-focus-within:text-plasma-blue transition-colors duration-300">Last Name</label>
                                <input type="text" class="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-plasma-blue/20 focus:border-plasma-blue transition-all duration-300 bg-white/80 backdrop-blur-sm hover:bg-white" placeholder="Doe">
                            </div>
                        </div>
                        <div class="group">
                            <label class="block text-gray-700 font-semibold mb-3 group-focus-within:text-plasma-blue transition-colors duration-300">Email</label>
                            <input type="email" class="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-plasma-blue/20 focus:border-plasma-blue transition-all duration-300 bg-white/80 backdrop-blur-sm hover:bg-white" placeholder="<EMAIL>">
                        </div>
                        <div class="group">
                            <label class="block text-gray-700 font-semibold mb-3 group-focus-within:text-plasma-blue transition-colors duration-300">Project Type</label>
                            <select class="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-plasma-blue/20 focus:border-plasma-blue transition-all duration-300 bg-white/80 backdrop-blur-sm hover:bg-white">
                                <option>Web Development</option>
                                <option>Graphic Design</option>
                                <option>Both Web Development & Graphic Design</option>
                                <option>Other</option>
                            </select>
                        </div>
                        <div class="group">
                            <label class="block text-gray-700 font-semibold mb-3 group-focus-within:text-plasma-blue transition-colors duration-300">Message</label>
                            <textarea rows="6" class="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-plasma-blue/20 focus:border-plasma-blue transition-all duration-300 bg-white/80 backdrop-blur-sm hover:bg-white resize-none" placeholder="Tell us about your project..."></textarea>
                        </div>
                        <button type="submit" class="group w-full bg-gradient-to-r from-plasma-blue to-plasma-accent text-white py-5 rounded-2xl font-bold text-lg hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-500 relative overflow-hidden">
                            <span class="relative z-10 flex items-center justify-center gap-2">
                                Send Message
                                <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                            </span>
                            <div class="absolute inset-0 bg-gradient-to-r from-plasma-accent to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        </button>
                    </form>
                </div>

                <!-- Contact Information -->
                <div class="space-y-12">
                    <div>
                        <h3 class="text-3xl font-bold mb-8 text-gray-900">Get in touch</h3>
                        <p class="text-gray-600 mb-12 leading-relaxed text-lg font-light">
                            We're here to help bring your ideas to life. Reach out to us through any of the following channels, and we'll get back to you within 24 hours.
                        </p>
                    </div>

                    <!-- Contact Methods -->
                    <div class="space-y-8">
                        <div class="group flex items-start space-x-6 p-6 rounded-2xl hover:bg-white/50 transition-all duration-300">
                            <div class="w-16 h-16 bg-gradient-to-br from-plasma-blue to-plasma-accent rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-bold text-gray-900 mb-3 text-lg">Email</h4>
                                <p class="text-gray-600 font-medium"><EMAIL></p>
                                <p class="text-gray-600 font-medium"><EMAIL></p>
                            </div>
                        </div>

                        <div class="group flex items-start space-x-6 p-6 rounded-2xl hover:bg-white/50 transition-all duration-300">
                            <div class="w-16 h-16 bg-gradient-to-br from-plasma-accent to-purple-500 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-bold text-gray-900 mb-3 text-lg">Phone</h4>
                                <p class="text-gray-600 font-medium">+****************</p>
                                <p class="text-gray-600 font-medium">+****************</p>
                            </div>
                        </div>

                        <div class="group flex items-start space-x-6 p-6 rounded-2xl hover:bg-white/50 transition-all duration-300">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-bold text-gray-900 mb-3 text-lg">Office</h4>
                                <p class="text-gray-600 font-medium">123 Creative Street</p>
                                <p class="text-gray-600 font-medium">Design District, NY 10001</p>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="pt-12 border-t border-gray-200/50">
                        <h4 class="font-bold text-gray-900 mb-8 text-lg">Follow us</h4>
                        <div class="flex space-x-4">
                            <a href="#" class="group w-14 h-14 bg-gradient-to-br from-plasma-blue to-plasma-accent rounded-2xl flex items-center justify-center text-white hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-500">
                                <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                            </a>
                            <a href="#" class="group w-14 h-14 bg-gradient-to-br from-plasma-accent to-purple-500 rounded-2xl flex items-center justify-center text-white hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-500">
                                <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                            <a href="#" class="group w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-white hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-500">
                                <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="relative bg-gradient-to-br from-plasma-dark via-gray-900 to-plasma-dark text-white py-20 overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-20 left-20 w-72 h-72 bg-plasma-blue rounded-full blur-3xl animate-pulse"></div>
            <div class="absolute bottom-20 right-20 w-96 h-96 bg-plasma-accent rounded-full blur-3xl animate-pulse" style="animation-delay: -2s;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid md:grid-cols-4 gap-12">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="text-4xl font-black text-gradient mb-8">
                        Plasma
                    </div>
                    <p class="text-gray-300 mb-10 max-w-md leading-relaxed text-lg font-light">
                        Igniting digital experiences through innovative web development and stunning graphic design. Let's create something extraordinary together.
                    </p>

                    <!-- Social Media with Modern Design -->
                    <div class="flex space-x-4">
                        <a href="#" class="group w-12 h-12 glass-dark rounded-2xl flex items-center justify-center text-gray-300 hover:text-white transition-all duration-500 hover:scale-110">
                            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="group w-12 h-12 glass-dark rounded-2xl flex items-center justify-center text-gray-300 hover:text-white transition-all duration-500 hover:scale-110">
                            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" class="group w-12 h-12 glass-dark rounded-2xl flex items-center justify-center text-gray-300 hover:text-white transition-all duration-500 hover:scale-110">
                            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Services -->
                <div>
                    <h4 class="text-xl font-bold mb-8 text-white">Services</h4>
                    <ul class="space-y-4 text-gray-300">
                        <li><a href="#" class="hover:text-plasma-blue transition-colors duration-300 font-medium hover:translate-x-1 transform transition-transform">Web Development</a></li>
                        <li><a href="#" class="hover:text-plasma-blue transition-colors duration-300 font-medium hover:translate-x-1 transform transition-transform">Graphic Design</a></li>
                        <li><a href="#" class="hover:text-plasma-blue transition-colors duration-300 font-medium hover:translate-x-1 transform transition-transform">Brand Identity</a></li>
                        <li><a href="#" class="hover:text-plasma-blue transition-colors duration-300 font-medium hover:translate-x-1 transform transition-transform">UI/UX Design</a></li>
                    </ul>
                </div>

                <!-- Company -->
                <div>
                    <h4 class="text-xl font-bold mb-8 text-white">Company</h4>
                    <ul class="space-y-4 text-gray-300">
                        <li><a href="#about" class="hover:text-plasma-accent transition-colors duration-300 font-medium hover:translate-x-1 transform transition-transform">About</a></li>
                        <li><a href="#portfolio" class="hover:text-plasma-accent transition-colors duration-300 font-medium hover:translate-x-1 transform transition-transform">Portfolio</a></li>
                        <li><a href="#contact" class="hover:text-plasma-accent transition-colors duration-300 font-medium hover:translate-x-1 transform transition-transform">Contact</a></li>
                        <li><a href="#" class="hover:text-plasma-accent transition-colors duration-300 font-medium hover:translate-x-1 transform transition-transform">Careers</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="border-t border-gray-700/50 mt-16 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 font-light">&copy; 2024 Plasma Agency. All rights reserved.</p>
                    <p class="text-gray-400 font-light mt-4 md:mt-0">Designed with <span class="text-plasma-blue">⚡</span> by Plasma</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Dark Mode Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const darkModeToggle = document.getElementById('darkModeToggle');
            const darkModeToggleMobile = document.getElementById('darkModeToggleMobile');
            const sunIcon = document.getElementById('sunIcon');
            const moonIcon = document.getElementById('moonIcon');
            const sunIconMobile = document.getElementById('sunIconMobile');
            const moonIconMobile = document.getElementById('moonIconMobile');
            const html = document.documentElement;

            // Check for saved theme preference or default to light mode
            const currentTheme = localStorage.getItem('theme') || 'light';

            // Apply the current theme
            if (currentTheme === 'dark') {
                html.classList.add('dark');
                updateIcons(true);
            } else {
                html.classList.remove('dark');
                updateIcons(false);
            }

            // Function to update icons
            function updateIcons(isDark) {
                if (isDark) {
                    sunIcon.classList.add('hidden');
                    moonIcon.classList.remove('hidden');
                    sunIconMobile.classList.add('hidden');
                    moonIconMobile.classList.remove('hidden');
                } else {
                    sunIcon.classList.remove('hidden');
                    moonIcon.classList.add('hidden');
                    sunIconMobile.classList.remove('hidden');
                    moonIconMobile.classList.add('hidden');
                }
            }

            // Function to toggle dark mode
            function toggleDarkMode() {
                const isDark = html.classList.contains('dark');

                if (isDark) {
                    html.classList.remove('dark');
                    localStorage.setItem('theme', 'light');
                    updateIcons(false);
                } else {
                    html.classList.add('dark');
                    localStorage.setItem('theme', 'dark');
                    updateIcons(true);
                }
            }

            // Add event listeners
            darkModeToggle.addEventListener('click', toggleDarkMode);
            darkModeToggleMobile.addEventListener('click', toggleDarkMode);

            console.log('Plasma Agency website loaded successfully with dark mode!');
        });
    </script>
</body>
</html>
